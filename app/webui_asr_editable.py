#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一语音识别 WebUI (可编辑版本)
支持 Paraformer、Seaco、SenseVoice 三种模型类型
基于Gradio构建，支持时间戳、情感识别、说话人识别和表格编辑
"""

import sys
import os
import configparser
import re
import platform
import time
import threading
import tempfile
from typing import List, Dict, Tuple, Optional, Any
from datetime import datetime
import json

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

try:
    import gradio as gr
    import pandas as pd
    import funasr
except ImportError as e:
    print(f"请安装必要的依赖: {e}")
    sys.exit(1)

# Web缓存管理器
class WebCacheManager:
    def __init__(self, base_cache_dir: str = None):
        if base_cache_dir is None:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            base_cache_dir = os.path.join(script_dir, "web_cache")
        
        self.base_cache_dir = base_cache_dir
        self.current_session_dir = None
        self.metadata = {}
        
        os.makedirs(base_cache_dir, exist_ok=True)
    
    def create_session(self, audio_filename: str) -> str:
        """创建新的识别会话"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        session_name = f"{audio_filename}_{timestamp}"
        self.current_session_dir = os.path.join(self.base_cache_dir, session_name)
        
        os.makedirs(self.current_session_dir, exist_ok=True)
        
        self.metadata = {
            "session_id": session_name,
            "audio_filename": audio_filename,
            "created_time": timestamp,
            "total_segments": 0,
            "last_modified": timestamp
        }
        
        self._save_metadata()
        return self.current_session_dir
    
    def save_recognition_result(self, table_data: List[List], model_type: str) -> bool:
        """保存识别结果到分割文件"""
        if not self.current_session_dir:
            raise ValueError("没有活动的会话，请先创建会话")
        
        try:
            for i, row_data in enumerate(table_data, 1):
                segment_id = f"{i:03d}"
                
                # 根据模型类型保存不同格式的数据
                if model_type == "sensevoice":
                    # SenseVoice格式：[编号, 开始时间, 结束时间, 情感, 事件, 识别文本]
                    segment_info = {
                        "id": row_data[0],
                        "start_time": row_data[1], 
                        "end_time": row_data[2],
                        "emotion": row_data[3] if len(row_data) > 3 else "",
                        "event": row_data[4] if len(row_data) > 4 else "",
                        "text": row_data[5] if len(row_data) > 5 else ""
                    }
                else:
                    # Paraformer/Seaco格式：[编号, 开始时间, 结束时间, 说话人, 识别文本]
                    segment_info = {
                        "id": row_data[0],
                        "start_time": row_data[1],
                        "end_time": row_data[2], 
                        "speaker": row_data[3] if len(row_data) > 3 else "",
                        "text": row_data[4] if len(row_data) > 4 else ""
                    }
                
                segment_file = os.path.join(self.current_session_dir, f"{segment_id}.json")
                with open(segment_file, 'w', encoding='utf-8') as f:
                    json.dump(segment_info, f, ensure_ascii=False, indent=2)
            
            self.metadata["total_segments"] = len(table_data)
            self.metadata["last_modified"] = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.metadata["model_type"] = model_type
            self._save_metadata()
            
            return True
            
        except Exception as e:
            print(f"保存识别结果失败: {e}")
            return False
    
    def update_segment(self, segment_id: int, **kwargs) -> bool:
        """更新指定片段的内容"""
        if not self.current_session_dir:
            return False
        
        segment_file = os.path.join(self.current_session_dir, f"{segment_id:03d}.json")
        
        if not os.path.exists(segment_file):
            return False
        
        try:
            with open(segment_file, 'r', encoding='utf-8') as f:
                segment_info = json.load(f)
            
            # 更新传入的任何字段
            for key, value in kwargs.items():
                if value is not None:
                    segment_info[key] = value
            
            with open(segment_file, 'w', encoding='utf-8') as f:
                json.dump(segment_info, f, ensure_ascii=False, indent=2)
            
            self.metadata["last_modified"] = datetime.now().strftime("%Y%m%d_%H%M%S")
            self._save_metadata()
            
            return True
            
        except Exception as e:
            print(f"更新片段失败: {e}")
            return False
    
    def load_session_data(self, session_dir: str) -> Optional[List[List]]:
        """加载会话数据"""
        if not os.path.exists(session_dir):
            return None
        
        try:
            metadata_file = os.path.join(session_dir, "metadata.json")
            if not os.path.exists(metadata_file):
                return None
            
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            table_data = []
            total_segments = metadata.get("total_segments", 0)
            model_type = metadata.get("model_type", "paraformer")
            
            for i in range(1, total_segments + 1):
                segment_file = os.path.join(session_dir, f"{i:03d}.json")
                if os.path.exists(segment_file):
                    with open(segment_file, 'r', encoding='utf-8') as f:
                        segment_info = json.load(f)
                    
                    if model_type == "sensevoice":
                        row = [
                            segment_info.get("id", i),
                            segment_info.get("start_time", ""),
                            segment_info.get("end_time", ""),
                            segment_info.get("emotion", ""),
                            segment_info.get("event", ""),
                            segment_info.get("text", "")
                        ]
                    else:
                        row = [
                            segment_info.get("id", i),
                            segment_info.get("start_time", ""),
                            segment_info.get("end_time", ""),
                            segment_info.get("speaker", ""),
                            segment_info.get("text", "")
                        ]
                    
                    table_data.append(row)
            
            return table_data
            
        except Exception as e:
            print(f"加载会话数据失败: {e}")
            return None
    
    def _save_metadata(self):
        """保存元数据"""
        if self.current_session_dir:
            metadata_file = os.path.join(self.current_session_dir, "metadata.json")
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)

class AsrWebUI:
    def __init__(self):
        # 模型缓存和配置数据存储
        self.asr_models_config = {}
        self.vad_models_config = {}
        self.punc_models_config = {}
        self.spk_models_config = {}
        self.sensevoice_models_config = {}
        self.model_cache = {}
        
        # 当前识别结果
        self.current_recognition_result = ""
        self.current_audio_filename = ""
        
        # Web缓存管理器
        self.cache_manager = WebCacheManager()
        
        # 情感和事件标签（SenseVoice用）
        self.emotion_labels = ['(无)', '<|HAPPY|>', '<|SAD|>', '<|ANGRY|>', '<|NEUTRAL|>', '<|FEARFUL|>', '<|DISGUSTED|>', '<|SURPRISED|>']
        self.event_labels = ['(无)', '<|BGM|>', '<|Speech|>', '<|Applause|>', '<|Laughter|>', '<|Cry|>', '<|Sneeze|>', '<|Breath|>', '<|Cough|>']
        
        # 加载模型配置
        self.load_model_config()
    
    def load_model_config(self):
        """加载模型配置文件"""
        config_paths = [
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "model.conf"),
            # os.path.join(os.path.dirname(os.path.abspath(__file__)), "model_mac.conf"),
        ]
        
        config = configparser.ConfigParser()
        loaded_path = None
        for path in config_paths:
            if os.path.exists(path):
                try:
                    config.read(path, encoding='utf-8')
                    loaded_path = path
                    break
                except Exception as e:
                    print(f"读取配置文件 {path} 失败: {e}")
                    continue

        if not loaded_path:
            print("错误: 未找到 model.conf 文件。请确保它位于项目目录下。")
            return

        print(f"成功从 {loaded_path} 加载模型配置。")

        def populate_config(section_name, model_dict):
            model_dict.clear()
            if section_name in config:
                for name, path_or_id in config[section_name].items():
                    model_dict[name] = path_or_id
            else:
                print(f"警告: 配置文件中未找到 [{section_name}] 部分。")

        # 加载各类模型配置
        populate_config("asr_models_dir", self.asr_models_config)
        populate_config("asr_seaco_models_dir", self.asr_models_config)  # Seaco模型会覆盖或添加
        populate_config("asr_sense_model_dir", self.sensevoice_models_config)
        populate_config("vad_models_dir", self.vad_models_config)
        populate_config("punc_models_dir", self.punc_models_config)
        populate_config("spk_models_dir", self.spk_models_config)

    def get_model_choices(self, model_type: str):
        """根据模型类型获取对应的模型选项"""
        if model_type == "paraformer":
            # 重新加载paraformer模型
            config = configparser.ConfigParser()
            config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "model.conf")
            if os.path.exists(config_path):
                config.read(config_path, encoding='utf-8')
                asr_models = {}
                if "asr_models_dir" in config:
                    for name, path_or_id in config["asr_models_dir"].items():
                        asr_models[name] = path_or_id
                return list(asr_models.keys())
        elif model_type == "seaco":
            # 重新加载seaco模型
            config = configparser.ConfigParser()
            config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "model.conf")
            if os.path.exists(config_path):
                config.read(config_path, encoding='utf-8')
                asr_models = {}
                if "asr_seaco_models_dir" in config:
                    for name, path_or_id in config["asr_seaco_models_dir"].items():
                        asr_models[name] = path_or_id
                return list(asr_models.keys())
        elif model_type == "sensevoice":
            return list(self.sensevoice_models_config.keys())
        
        return list(self.asr_models_config.keys())

    def get_vad_model_choices(self):
        """获取VAD模型选项"""
        choices = ["None (不使用)"] + list(self.vad_models_config.keys())
        return choices

    def get_punc_model_choices(self):
        """获取标点模型选项"""
        choices = ["None (不使用)"] + list(self.punc_models_config.keys())
        return choices

    def get_spk_model_choices(self):
        """获取说话人模型选项"""
        choices = ["None (不使用)"] + list(self.spk_models_config.keys())
        return choices

    def update_model_choices(self, model_type: str):
        """根据模型类型更新ASR模型选项和界面显示"""
        choices = self.get_model_choices(model_type)
        
        # 根据模型类型确定界面显示状态
        hotword_visible = (model_type == "seaco")
        spk_visible = (model_type in ["paraformer", "seaco"])
        emotion_visible = (model_type == "sensevoice")
        
        return (
            gr.Dropdown(choices=choices, value=choices[0] if choices else None),  # ASR模型下拉框
            gr.Row(visible=hotword_visible),  # 热词输入行
            gr.Row(visible=spk_visible),  # 说话人配置行
            gr.Row(visible=emotion_visible),  # 情感配置行 (SenseVoice)
        )

    def format_timestamp(self, milliseconds: int, srt_format: bool = False) -> str:
        """将毫秒转换为时间戳格式"""
        seconds = milliseconds // 1000
        ms = milliseconds % 1000
        minutes = seconds // 60
        seconds %= 60
        hours = minutes // 60
        minutes %= 60
        if srt_format:
            return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d},{int(ms):03d}"
        else:
            return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}.{int(ms):03d}"

    def start_recognition(
        self, 
        audio_file,
        model_type: str,
        device: str,
        asr_model: str,
        vad_model: str,
        punc_model: str,
        spk_model: str,
        speaker_enabled: bool,
        speaker_count: int,
        hotwords: str = "",
        language: str = "zh",
        use_itn: bool = False
    ):
        """开始语音识别"""
        
        if audio_file is None:
            return "请先上传音频文件", None, "没有音频文件"
        
        try:
            # 获取音频文件名
            audio_filename = os.path.splitext(os.path.basename(audio_file.name))[0]
            self.current_audio_filename = audio_filename
            
            # 创建新的识别会话
            session_dir = self.cache_manager.create_session(audio_filename)
            
            # 构建模型键
            model_key = f"{model_type}_{device}_{asr_model}_{vad_model}_{punc_model}_{spk_model}"
            
            # 检查模型缓存
            if model_key not in self.model_cache:
                yield f"正在加载 {model_type} 模型...", None, "加载中"
                
                # 根据模型类型加载不同的模型
                if model_type == "sensevoice":
                    model = self._load_sensevoice_model(asr_model, device, use_itn)
                else:
                    model = self._load_paraformer_model(
                        model_type, asr_model, vad_model, punc_model, spk_model, 
                        device, speaker_enabled
                    )
                
                if model is None:
                    yield "模型加载失败", None, "错误"
                    return
                
                self.model_cache[model_key] = model
                yield f"{model_type} 模型加载完成", None, "就绪"
            else:
                yield f"使用缓存的 {model_type} 模型", None, "就绪"
            
            model = self.model_cache[model_key]
            yield "开始识别...", None, "识别中"
            
            # 执行识别
            if model_type == "sensevoice":
                result = self._recognize_sensevoice(model, audio_file.name, language, use_itn)
            else:
                result = self._recognize_paraformer(
                    model, audio_file.name, hotwords, speaker_count, speaker_enabled
                )
            
            if not result:
                yield "识别失败", None, "错误"
                return
            
            # 格式化结果为表格数据
            table_data = self._format_recognition_result(result, model_type)
            
            # 保存识别结果到缓存
            self.cache_manager.save_recognition_result(table_data, model_type)
            
            # 生成DataFrame用于显示
            df = self._create_dataframe(table_data, model_type)
            
            yield "识别完成", df, "完成"
            
        except Exception as e:
            yield f"识别过程中出现错误: {str(e)}", None, "错误"

    def _load_sensevoice_model(self, asr_model: str, device: str, use_itn: bool):
        """加载SenseVoice模型"""
        try:
            if asr_model not in self.sensevoice_models_config:
                print(f"未找到SenseVoice模型: {asr_model}")
                return None
                
            model_path = self.sensevoice_models_config[asr_model]
            
            model = funasr.AutoModel(
                model=model_path,
                device=device,
                disable_update=True,
                use_itn=use_itn
            )
            
            return model
            
        except Exception as e:
            print(f"加载SenseVoice模型失败: {e}")
            return None

    def _load_paraformer_model(self, model_type: str, asr_model: str, vad_model: str, 
                              punc_model: str, spk_model: str, device: str, speaker_enabled: bool):
        """加载Paraformer/Seaco模型"""
        try:
            # 构建模型参数
            model_params = {}
            
            # ASR模型
            if model_type == "seaco":
                config = configparser.ConfigParser()
                config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "model.conf")
                if os.path.exists(config_path):
                    config.read(config_path, encoding='utf-8')
                    if "asr_seaco_models_dir" in config and asr_model in config["asr_seaco_models_dir"]:
                        model_params["model"] = config["asr_seaco_models_dir"][asr_model]
            else:
                if asr_model in self.asr_models_config:
                    model_params["model"] = self.asr_models_config[asr_model]
            
            # VAD模型
            if vad_model != "None (不使用)" and vad_model in self.vad_models_config:
                model_params["vad_model"] = self.vad_models_config[vad_model]
            
            # 标点模型
            if punc_model != "None (不使用)" and punc_model in self.punc_models_config:
                model_params["punc_model"] = self.punc_models_config[punc_model]
            
            # 说话人模型
            if speaker_enabled and spk_model != "None (不使用)" and spk_model in self.spk_models_config:
                model_params["spk_model"] = self.spk_models_config[spk_model]
            
            model_params["device"] = device
            model_params["disable_update"] = True
            
            model = funasr.AutoModel(**model_params)
            return model
            
        except Exception as e:
            print(f"加载Paraformer模型失败: {e}")
            return None

    def _recognize_sensevoice(self, model, audio_path: str, language: str, use_itn: bool):
        """使用SenseVoice进行识别"""
        try:
            result = model.generate(
                input=audio_path,
                language=language,
                use_itn=use_itn
            )
            return result
        except Exception as e:
            print(f"SenseVoice识别失败: {e}")
            return None

    def _recognize_paraformer(self, model, audio_path: str, hotwords: str, 
                             speaker_count: int, speaker_enabled: bool):
        """使用Paraformer/Seaco进行识别"""
        try:
            params = {"input": audio_path}
            
            # 添加热词支持（仅Seaco）
            if hotwords.strip():
                hotwords_list = [word.strip() for word in hotwords.split() if word.strip()]
                if hotwords_list:
                    params["hotword"] = " ".join(hotwords_list)
            
            # 说话人识别参数
            if speaker_enabled and speaker_count > 0:
                params["spk_mode"] = "spk_id"
            
            result = model.generate(**params)
            return result
        except Exception as e:
            print(f"Paraformer识别失败: {e}")
            return None

    def _format_recognition_result(self, result, model_type: str):
        """格式化识别结果为表格数据"""
        table_data = []
        
        if not result:
            return table_data
        
        # 处理结果格式
        if isinstance(result, list) and len(result) > 0:
            first_result = result[0]
        else:
            first_result = result
        
        if "sentence_info" in first_result:
            sentences = first_result["sentence_info"]
            for i, sentence in enumerate(sentences, 1):
                start_time = self.format_timestamp(sentence.get("start", 0))
                end_time = self.format_timestamp(sentence.get("end", 0))
                text = sentence.get("text", "")
                
                if model_type == "sensevoice":
                    # SenseVoice格式：提取情感和事件标签
                    emotion = "(无)"
                    event = "(无)"
                    
                    # 简单的标签提取（可以根据实际需要优化）
                    for label in self.emotion_labels[1:]:  # 跳过"(无)"
                        if label in text:
                            emotion = label
                            text = text.replace(label, "").strip()
                            break
                    
                    for label in self.event_labels[1:]:  # 跳过"(无)"
                        if label in text:
                            event = label
                            text = text.replace(label, "").strip()
                            break
                    
                    row = [i, start_time, end_time, emotion, event, text]
                else:
                    # Paraformer/Seaco格式：说话人信息
                    speaker = sentence.get("spk", "")
                    if speaker:
                        speaker = f"说话人{speaker}"
                    row = [i, start_time, end_time, speaker, text]
                
                table_data.append(row)
        
        return table_data

    def _create_dataframe(self, table_data: List[List], model_type: str):
        """创建用于显示的DataFrame"""
        if model_type == "sensevoice":
            columns = ["编号", "开始时间", "结束时间", "情感", "事件", "识别文本"]
        else:
            columns = ["编号", "开始时间", "结束时间", "说话人", "识别文本"]
        
        if not table_data:
            return pd.DataFrame(columns=columns)
        
        df = pd.DataFrame(table_data, columns=columns)
        return df

    def update_table_cell(self, df, row: int, col: str, value: str):
        """更新表格单元格"""
        try:
            if df is None or df.empty:
                return df, "表格为空"
            
            # 更新DataFrame
            df.loc[row, col] = value
            
            # 同时更新缓存
            segment_id = row + 1  # 行索引+1为片段ID
            
            if col == "情感":
                self.cache_manager.update_segment(segment_id, emotion=value)
            elif col == "事件":
                self.cache_manager.update_segment(segment_id, event=value)
            elif col == "识别文本":
                self.cache_manager.update_segment(segment_id, text=value)
            elif col == "说话人":
                self.cache_manager.update_segment(segment_id, speaker=value)
            
            return df, f"已更新第{row+1}行的{col}"
            
        except Exception as e:
            return df, f"更新失败: {str(e)}"

    def export_to_txt(self, df):
        """导出为TXT格式"""
        if df is None or df.empty:
            return None, "没有数据可导出"
        
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
                for _, row in df.iterrows():
                    if "识别文本" in row:
                        text = row["识别文本"]
                        start_time = row["开始时间"]
                        end_time = row["结束时间"]
                        
                        if "说话人" in row and row["说话人"]:
                            speaker_info = f"[{row['说话人']}] "
                        else:
                            speaker_info = ""
                        
                        f.write(f"[{start_time} --> {end_time}] {speaker_info}{text}\n")
                
                return f.name, "TXT导出成功"
        
        except Exception as e:
            return None, f"导出失败: {str(e)}"

    def export_to_srt(self, df):
        """导出为SRT格式"""
        if df is None or df.empty:
            return None, "没有数据可导出"
        
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False, encoding='utf-8') as f:
                for i, (_, row) in enumerate(df.iterrows(), 1):
                    if "识别文本" in row:
                        text = row["识别文本"]
                        start_time = self._convert_to_srt_time(row["开始时间"])
                        end_time = self._convert_to_srt_time(row["结束时间"])
                        
                        if "说话人" in row and row["说话人"]:
                            text = f"[{row['说话人']}] {text}"
                        
                        f.write(f"{i}\n")
                        f.write(f"{start_time} --> {end_time}\n")
                        f.write(f"{text}\n\n")
                
                return f.name, "SRT导出成功"
        
        except Exception as e:
            return None, f"导出失败: {str(e)}"

    def _convert_to_srt_time(self, timestamp: str) -> str:
        """将时间戳转换为SRT格式"""
        try:
            # 假设输入格式为 HH:MM:SS.mmm
            if '.' in timestamp:
                time_part, ms_part = timestamp.split('.')
                ms_part = ms_part[:3].ljust(3, '0')  # 确保毫秒部分为3位
                return f"{time_part},{ms_part}"
            else:
                return f"{timestamp},000"
        except:
            return timestamp

    def create_interface(self):
        """创建Gradio界面"""
        with gr.Blocks(title="LightASR 统一语音识别 WebUI", theme=gr.themes.Soft()) as interface:
            gr.Markdown("# 🎤 LightASR 统一语音识别 WebUI")
            gr.Markdown("支持 Paraformer、Seaco、SenseVoice 三种模型类型，可编辑识别结果")
            
            with gr.Row():
                with gr.Column(scale=1):
                    # 模型配置区域
                    gr.Markdown("## 📋 模型配置")
                    
                    model_type = gr.Dropdown(
                        choices=["paraformer", "seaco", "sensevoice"],
                        value="paraformer",
                        label="模型类型"
                    )
                    
                    device = gr.Dropdown(
                        choices=["cpu", "cuda"],
                        value="cuda" if platform.system().lower() != 'darwin' else "cpu",
                        label="运行设备"
                    )
                    
                    asr_model = gr.Dropdown(
                        choices=self.get_model_choices("paraformer"),
                        label="ASR模型"
                    )
                    
                    vad_model = gr.Dropdown(
                        choices=self.get_vad_model_choices(),
                        value="fsmn_vad",
                        label="VAD模型"
                    )
                    
                    punc_model = gr.Dropdown(
                        choices=self.get_punc_model_choices(),
                        value="punc_ct",
                        label="标点模型"
                    )
                    
                    with gr.Row(visible=True) as spk_row:
                        spk_model = gr.Dropdown(
                            choices=self.get_spk_model_choices(),
                            value="campplus_sv",
                            label="说话人模型"
                        )
                    
                    with gr.Row(visible=False) as hotword_row:
                        hotwords = gr.Textbox(
                            label="热词 (空格分隔)",
                            placeholder="请输入热词，使用空格分隔"
                        )
                    
                    with gr.Row(visible=False) as emotion_row:
                        language = gr.Dropdown(
                            choices=["zh", "en", "ja", "ko"],
                            value="zh",
                            label="语言"
                        )
                        use_itn = gr.Checkbox(
                            label="使用ITN(文本标准化)",
                            value=False
                        )
                    
                    speaker_enabled = gr.Checkbox(
                        label="启用说话人识别",
                        value=True
                    )
                    
                    speaker_count = gr.Slider(
                        minimum=0,
                        maximum=10,
                        value=0,
                        step=1,
                        label="说话人数量 (0=自动)"
                    )
                    
                    # 文件上传
                    gr.Markdown("## 📁 音频文件")
                    audio_file = gr.File(
                        label="上传音频文件",
                        file_types=["audio"]
                    )
                    
                    # 识别按钮
                    recognize_btn = gr.Button("🎯 开始识别", variant="primary", size="lg")
                    
                    # 状态显示
                    status = gr.Textbox(
                        label="状态",
                        value="等待开始",
                        interactive=False
                    )
                
                with gr.Column(scale=2):
                    # 结果显示区域
                    gr.Markdown("## 📊 识别结果")
                    
                    result_table = gr.Dataframe(
                        label="识别结果 (可编辑)",
                        interactive=True,
                        wrap=True
                    )
                    
                    # 导出按钮
                    with gr.Row():
                        export_txt_btn = gr.Button("📄 导出TXT")
                        export_srt_btn = gr.Button("🎬 导出SRT")
                    
                    # 导出文件下载
                    download_file = gr.File(label="下载文件", visible=False)
                    
                    # 操作提示
                    operation_info = gr.Textbox(
                        label="操作提示",
                        value="上传音频文件并选择模型配置后，点击开始识别",
                        interactive=False
                    )
            
            # 事件绑定
            model_type.change(
                fn=self.update_model_choices,
                inputs=[model_type],
                outputs=[asr_model, hotword_row, spk_row, emotion_row]
            )
            
            recognize_btn.click(
                fn=self.start_recognition,
                inputs=[
                    audio_file, model_type, device, asr_model, vad_model,
                    punc_model, spk_model, speaker_enabled, speaker_count,
                    hotwords, language, use_itn
                ],
                outputs=[operation_info, result_table, status]
            )
            
            export_txt_btn.click(
                fn=self.export_to_txt,
                inputs=[result_table],
                outputs=[download_file, operation_info]
            )
            
            export_srt_btn.click(
                fn=self.export_to_srt,
                inputs=[result_table],
                outputs=[download_file, operation_info]
            )
        
        return interface

def main():
    """主函数"""
    # 创建WebUI实例
    webui = AsrWebUI()
    
    # 创建界面
    interface = webui.create_interface()
    
    # 启动服务
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True,
        inbrowser=True
    )

if __name__ == "__main__":
    main()
