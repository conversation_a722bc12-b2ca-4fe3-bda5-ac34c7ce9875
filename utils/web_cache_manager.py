#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web缓存管理器 - 按时间戳序号分割存储识别结果
"""

import os
import json
import time
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import pandas as pd

class WebCacheManager:
    def __init__(self, base_cache_dir: str = None):
        # 如果没有指定缓存目录，则使用脚本执行路径下的 web_cache
        if base_cache_dir is None:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            # 上一级目录（从 utils 目录回到 paraformer_spk_timestamp）
            parent_dir = os.path.dirname(script_dir)
            base_cache_dir = os.path.join(parent_dir, "web_cache")
        
        self.base_cache_dir = base_cache_dir
        self.current_session_dir = None
        self.metadata = {}
        
        # 确保缓存目录存在
        os.makedirs(base_cache_dir, exist_ok=True)
    
    def create_session(self, audio_filename: str) -> str:
        """创建新的识别会话"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        session_name = f"{audio_filename}_{timestamp}"
        self.current_session_dir = os.path.join(self.base_cache_dir, session_name)
        
        os.makedirs(self.current_session_dir, exist_ok=True)
        
        # 初始化元数据
        self.metadata = {
            "session_id": session_name,
            "audio_filename": audio_filename,
            "created_time": timestamp,
            "total_segments": 0,
            "last_modified": timestamp
        }
        
        self._save_metadata()
        return self.current_session_dir
    
    def save_recognition_result(self, table_data: List[List], speaker_enabled: bool = True) -> bool:
        """保存识别结果到分割文件"""
        if not self.current_session_dir:
            raise ValueError("没有活动的会话，请先创建会话")

        try:
            for i, row_data in enumerate(table_data, 1):
                segment_id = f"{i:03d}"

                if len(row_data) >= 6:
                    # SenseVoice格式：[编号, 开始时间, 结束时间, 情感, 事件, 识别文本]
                    segment_info = {
                        "id": row_data[0],
                        "start_time": row_data[1],
                        "end_time": row_data[2],
                        "emotion": row_data[3],
                        "event": row_data[4],
                        "text": row_data[5]
                    }
                elif speaker_enabled and len(row_data) >= 5:
                    # Paraformer/Seaco格式：[编号, 开始时间, 结束时间, 说话人, 识别文本]
                    segment_info = {
                        "id": row_data[0],
                        "start_time": row_data[1],
                        "end_time": row_data[2],
                        "speaker": row_data[3],
                        "text": row_data[4]
                    }
                else:
                    # 简单格式：[编号, 开始时间, 结束时间, 识别文本]
                    segment_info = {
                        "id": row_data[0],
                        "start_time": row_data[1],
                        "end_time": row_data[2],
                        "speaker": "",
                        "text": row_data[3] if len(row_data) > 3 else ""
                    }

                # 保存片段信息到JSON文件
                segment_file = os.path.join(self.current_session_dir, f"{segment_id}.json")
                with open(segment_file, 'w', encoding='utf-8') as f:
                    json.dump(segment_info, f, ensure_ascii=False, indent=2)

            # 更新元数据
            self.metadata["total_segments"] = len(table_data)
            self.metadata["last_modified"] = datetime.now().strftime("%Y%m%d_%H%M%S")
            self._save_metadata()

            return True

        except Exception as e:
            print(f"保存识别结果失败: {e}")
            return False
    
    def update_segment(self, segment_id: int, speaker: str = None, text: str = None,
                      emotion: str = None, event: str = None) -> bool:
        """更新指定片段的内容"""
        if not self.current_session_dir:
            return False

        segment_file = os.path.join(self.current_session_dir, f"{segment_id:03d}.json")

        if not os.path.exists(segment_file):
            return False

        try:
            # 读取现有数据
            with open(segment_file, 'r', encoding='utf-8') as f:
                segment_info = json.load(f)

            # 更新内容
            if speaker is not None:
                segment_info["speaker"] = speaker
            if text is not None:
                segment_info["text"] = text
            if emotion is not None:
                segment_info["emotion"] = emotion
            if event is not None:
                segment_info["event"] = event

            # 保存更新
            with open(segment_file, 'w', encoding='utf-8') as f:
                json.dump(segment_info, f, ensure_ascii=False, indent=2)

            # 更新元数据时间戳
            self.metadata["last_modified"] = datetime.now().strftime("%Y%m%d_%H%M%S")
            self._save_metadata()

            return True

        except Exception as e:
            print(f"更新片段失败: {e}")
            return False
    
    def get_segment(self, segment_id: int) -> Optional[Dict]:
        """获取指定片段的信息"""
        if not self.current_session_dir:
            return None
        
        segment_file = os.path.join(self.current_session_dir, f"{segment_id:03d}.json")
        
        if not os.path.exists(segment_file):
            return None
        
        try:
            with open(segment_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"读取片段失败: {e}")
            return None
    
    def get_all_segments(self) -> List[Dict]:
        """获取所有片段信息，按序号排序"""
        if not self.current_session_dir:
            return []
        
        segments = []
        
        try:
            # 获取所有JSON文件并排序
            json_files = [f for f in os.listdir(self.current_session_dir) if f.endswith('.json') and f != 'metadata.json']
            json_files.sort()
            
            for json_file in json_files:
                segment_file = os.path.join(self.current_session_dir, json_file)
                with open(segment_file, 'r', encoding='utf-8') as f:
                    segment_info = json.load(f)
                    segments.append(segment_info)
            
            return segments
            
        except Exception as e:
            print(f"读取所有片段失败: {e}")
            return []
    
    def generate_srt_content(self) -> str:
        """生成SRT格式内容"""
        segments = self.get_all_segments()
        if not segments:
            return ""

        lines = []
        for segment in segments:
            lines.append(str(segment["id"]))
            lines.append(f"{segment['start_time']} --> {segment['end_time']}")

            # 组装文本内容
            text = segment['text']
            if segment.get("emotion") and segment.get("event"):
                # SenseVoice格式
                lines.append(f"[{segment['emotion']}][{segment['event']}] {text}")
            elif segment.get("speaker"):
                # Paraformer/Seaco格式
                lines.append(f"[{segment['speaker']}] {text}")
            else:
                lines.append(text)

            lines.append("")  # 空行分隔

        return '\n'.join(lines)

    def generate_txt_content(self) -> str:
        """生成TXT格式内容"""
        segments = self.get_all_segments()
        if not segments:
            return ""

        lines = []
        for segment in segments:
            text = segment['text']
            if segment.get("emotion") and segment.get("event"):
                # SenseVoice格式
                lines.append(f"[{segment['emotion']}][{segment['event']}] {text}")
            elif segment.get("speaker"):
                # Paraformer/Seaco格式
                lines.append(f"[{segment['speaker']}] {text}")
            else:
                lines.append(text)

        return '\n'.join(lines)
    
    def save_to_file(self, file_path: str, format_type: str = "srt") -> bool:
        """保存到文件"""
        try:
            if format_type.lower() == "srt":
                content = self.generate_srt_content()
            elif format_type.lower() == "txt":
                content = self.generate_txt_content()
            else:
                return False
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return True
            
        except Exception as e:
            print(f"保存文件失败: {e}")
            return False
    
    def load_session(self, session_dir: str) -> bool:
        """加载已存在的会话"""
        if not os.path.exists(session_dir):
            return False
        
        self.current_session_dir = session_dir
        
        # 加载元数据
        metadata_file = os.path.join(session_dir, "metadata.json")
        if os.path.exists(metadata_file):
            try:
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    self.metadata = json.load(f)
                return True
            except Exception as e:
                print(f"加载元数据失败: {e}")
                return False
        
        return False
    
    def get_sessions_list(self) -> List[Dict]:
        """获取所有会话列表"""
        sessions = []
        
        try:
            for item in os.listdir(self.base_cache_dir):
                session_path = os.path.join(self.base_cache_dir, item)
                if os.path.isdir(session_path):
                    metadata_file = os.path.join(session_path, "metadata.json")
                    if os.path.exists(metadata_file):
                        with open(metadata_file, 'r', encoding='utf-8') as f:
                            metadata = json.load(f)
                            sessions.append({
                                "session_dir": session_path,
                                "session_name": item,
                                **metadata
                            })
            
            # 按创建时间排序
            sessions.sort(key=lambda x: x.get("created_time", ""), reverse=True)
            return sessions
            
        except Exception as e:
            print(f"获取会话列表失败: {e}")
            return []
    
    def delete_session(self, session_dir: str) -> bool:
        """删除指定会话"""
        try:
            import shutil
            if os.path.exists(session_dir):
                shutil.rmtree(session_dir)
                return True
            return False
        except Exception as e:
            print(f"删除会话失败: {e}")
            return False
    
    def _save_metadata(self):
        """保存元数据"""
        if not self.current_session_dir:
            return
        
        metadata_file = os.path.join(self.current_session_dir, "metadata.json")
        try:
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存元数据失败: {e}")

    def get_session_stats(self) -> Dict:
        """获取当前会话统计信息"""
        if not self.current_session_dir:
            return {}
        
        stats = {
            "session_dir": self.current_session_dir,
            "total_segments": self.metadata.get("total_segments", 0),
            "created_time": self.metadata.get("created_time", ""),
            "last_modified": self.metadata.get("last_modified", ""),
            "audio_filename": self.metadata.get("audio_filename", "")
        }
        
        return stats 